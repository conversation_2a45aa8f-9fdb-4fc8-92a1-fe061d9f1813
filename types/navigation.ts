import { BottomTabScreenProps } from '@react-navigation/bottom-tabs';
import { CompositeScreenProps, NavigatorScreenParams } from '@react-navigation/native';
import { StackScreenProps } from '@react-navigation/stack';

// Define tab navigation parameters
export type TabParamList = {
  index: undefined;
  search: undefined;
  cart: undefined;
  orders: undefined;
  profile: undefined;
};

// Define root stack navigation parameters
export type RootStackParamList = {
  '(tabs)': NavigatorScreenParams<TabParamList>;
  modal: undefined;
  'product-details': { productId: string; productName: string };
  'category-products': { categoryId: string; categoryName: string };
  checkout: undefined;
  'order-details': { orderId: string };
  'address-form': { addressId?: string };
  notifications: undefined;
  settings: undefined;
  '+not-found': undefined;
};

// Helper types for screen props
export type RootStackScreenProps<T extends keyof RootStackParamList> = StackScreenProps<
  RootStackParamList,
  T
>;

export type TabScreenProps<T extends keyof TabParamList> = CompositeScreenProps<
  BottomTabScreenProps<TabParamList, T>,
  RootStackScreenProps<keyof RootStackParamList>
>;

// Type guard to check if a route name exists in TabParamList
export function isTabRoute(routeName: string): routeName is keyof TabParamList {
  return ['index', 'search', 'cart', 'orders', 'profile'].includes(routeName);
}

// Type guard to check if a route name exists in RootStackParamList
export function isStackRoute(routeName: string): routeName is keyof RootStackParamList {
  return [
    '(tabs)',
    'modal',
    'product-details',
    'category-products',
    'checkout',
    'order-details',
    'address-form',
    'notifications',
    'settings',
    '+not-found',
  ].includes(routeName);
}

// Declare global namespace for useNavigation hook
declare global {
  namespace ReactNavigation {
    interface RootParamList extends RootStackParamList {}
  }
} 